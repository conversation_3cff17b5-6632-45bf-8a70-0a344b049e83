from flask import Flask, render_template, request, redirect
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import plotly.graph_objs as go
import plotly
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://username:password@localhost/dbname'
db = SQLAlchemy(app)

class DailyData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    value = db.Column(db.Integer, nullable=False)

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        date = request.form['date']
        value = request.form['value']
        new_data = DailyData(date=datetime.strptime(date, '%Y-%m-%d'), value=int(value))
        db.session.add(new_data)
        db.session.commit()
        return redirect('/')
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    data = DailyData.query.order_by(DailyData.date).all()
    dates = [d.date.strftime('%Y-%m-%d') for d in data]
    values = [d.value for d in data]

    graph = [go.Scatter(x=dates, y=values, mode='lines+markers', name='Daily Data')]
    layout = dict(title='Live Data Chart', xaxis=dict(title='Date'), yaxis=dict(title='Value'))
    fig = dict(data=graph, layout=layout)
    chart_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)

    return render_template('dashboard.html', chart_json=chart_json)

if __name__ == '__main__':
    app.run(debug=True)
